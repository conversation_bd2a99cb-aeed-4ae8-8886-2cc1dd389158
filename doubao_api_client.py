#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包AI聊天API客户端
基于抓包信息实现的独立Python程序
"""

import requests
import json
import time
from typing import Dict, Any, Optional, Generator
import urllib.parse


class DoubaoAPIClient:
    """豆包AI聊天API客户端"""
    
    def __init__(self):
        self.base_url = "https://www.doubao.com"
        self.session = requests.Session()
        self._setup_headers()
    
    def _setup_headers(self):
        """设置请求头"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/138.0.3351.83',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Linux"',
            'Connection': 'keep-alive',
        })
    
    def _build_url_params(self) -> Dict[str, str]:
        """构建URL参数"""
        return {
            'aid': '497858',
            'device_id': '7532812407958914560',
            'device_platform': 'web',
            'language': 'zh',
            'pc_version': '2.29.3',
            'pkg_type': 'release_version',
            'real_aid': '497858',
            'region': 'CN',
            'samantha_web': '1',
            'sys_region': 'CN',
            'tea_uuid': '7532812413725918772',
            'use-olympus-account': '1',
            'version_code': '20800',
            'web_id': '7532812413725918772',
            # 注意：这些token需要从实际登录会话中获取
            'msToken': 'YOUR_MS_TOKEN_HERE',
            'a_bogus': 'YOUR_A_BOGUS_HERE'
        }
    
    def _build_request_payload(self, message: str, conversation_id: str = "0", 
                             local_conversation_id: Optional[str] = None) -> Dict[str, Any]:
        """构建请求载荷"""
        if local_conversation_id is None:
            local_conversation_id = f"local_{int(time.time() * 1000)}"
        
        return {
            "messages": [
                {
                    "content": json.dumps({"text": message}, ensure_ascii=False),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": conversation_id == "0",
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_deep_think": False,
                "use_auto_cot": True,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "evaluate_option": {
                "web_ab_params": ""
            },
            "conversation_id": conversation_id,
            "local_conversation_id": local_conversation_id,
            "local_message_id": f"msg_{int(time.time() * 1000000)}"
        }
    
    def send_message(self, message: str, conversation_id: str = "0", 
                    local_conversation_id: Optional[str] = None) -> Generator[Dict[str, Any], None, None]:
        """
        发送消息到豆包AI
        
        Args:
            message: 要发送的消息
            conversation_id: 对话ID，默认为"0"表示新对话
            local_conversation_id: 本地对话ID
            
        Yields:
            Dict: 服务器返回的事件数据
        """
        url = f"{self.base_url}/samantha/chat/completion"
        params = self._build_url_params()
        payload = self._build_request_payload(message, conversation_id, local_conversation_id)
        
        try:
            response = self.session.post(
                url,
                params=params,
                json=payload,
                stream=True,
                timeout=30
            )
            response.raise_for_status()
            
            # 解析Server-Sent Events (SSE)流
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])  # 去掉'data: '前缀
                        yield data
                    except json.JSONDecodeError:
                        continue
                        
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return
    
    def set_cookies(self, cookie_string: str):
        """设置Cookie"""
        # 解析cookie字符串并设置到session中
        for cookie in cookie_string.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                self.session.cookies.set(name, value)
    
    def set_tokens(self, ms_token: str, a_bogus: str):
        """设置必要的token"""
        # 更新URL参数中的token
        pass  # 在实际使用时需要更新_build_url_params方法中的token


def parse_response_content(event_data: Dict[str, Any]) -> Optional[str]:
    """解析响应内容"""
    try:
        if 'event_data' in event_data:
            data = json.loads(event_data['event_data'])
            if 'message' in data and 'content' in data['message']:
                content = json.loads(data['message']['content'])
                if 'text' in content:
                    return content['text']
    except (json.JSONDecodeError, KeyError):
        pass
    return None


def main():
    """主函数 - 示例用法"""
    client = DoubaoAPIClient()
    
    # 设置Cookie（需要从实际登录会话中获取）
    cookie_string = """
    hook_slardar_session_id=2025073017592839920BEBA2D613EF2863;
    ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c;
    i18next=zh;
    _ga=GA1.1.941630486.1753869579;
    flow_user_country=CN
    """
    client.set_cookies(cookie_string.strip())
    
    # 发送消息
    message = "你好，请介绍一下自己"
    print(f"发送消息: {message}")
    print("-" * 50)
    
    try:
        for event in client.send_message(message):
            content = parse_response_content(event)
            if content:
                print(f"AI回复: {content}")
            
            # 打印原始事件数据（调试用）
            print(f"事件类型: {event.get('event_type')}")
            print(f"事件ID: {event.get('event_id')}")
            print("-" * 30)
            
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
